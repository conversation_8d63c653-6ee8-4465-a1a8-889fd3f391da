
import jsony, ws, asyncdispatch, tables
import utils/log

type
  Bot* = ref object
    conn*: WebSocket

  GroupMsgParams* = object
    group_id*: int64
    message*: string

  PrivateMsgParams* = object
    user_id*: int64
    message*: string

  BotAction*[T] = object
    action*: string
    params*: T

proc newBot*(conn: WebSocket): Bot =
  Bot(conn: conn)

proc send*[T](bot: Bot, action: string, params: T) {.async.} =
  try:
    let payload = BotAction[T](action: action, params: params)
    await bot.conn.send(payload.toJson())
    logInfo("发送数据: " & payload.toJson())
  except Exception as e:
    logError("发送失败: " & e.msg)

proc sendGroupMsg*(bot: Bot, groupId: int64, message: string) {.async.} =
  let params = GroupMsgParams(group_id: groupId, message: message)
  await bot.send("send_group_msg", params)

proc sendPrivateMsg*(bot: Bot, userId: int64, message: string) {.async.} =
  let params = PrivateMsgParams(user_id: userId, message: message)
  await bot.send("send_private_msg", params)

# 绑定所有API方法到Bot 将api.nim中的所有API代码插入到此处开始
include api

