import times, strformat, strutils, math, os

type
  LogLevel* = enum
    Trace = 1, Debug, Info, Warn, <PERSON>rror, Fatal

  LogMode = object
    name: string
    color: string

  Logger* = ref object
    useColor*: bool
    outFile*: string
    level*: LogLevel
    fileName*: bool

const modes = [
  LogMode(name: "trace", color: "\27[34m"),
  LogMode(name: "debug", color: "\27[36m"),
  LogMode(name: "info", color: "\27[32m"),
  LogMode(name: "warn", color: "\27[33m"),
  LogMode(name: "error", color: "\27[31m"),
  LogMode(name: "fatal", color: "\27[35m")
]

var defaultLogger* = Logger(
  useColor: true,
  outFile: "",
  level: LogLevel.Trace,
  fileName: true
)

proc round(x: float, increment: float = 0.01): float =
  let scaled = x / increment
  if scaled > 0:
    floor(scaled + 0.5) * increment
  else:
    ceil(scaled - 0.5) * increment

proc toLogString(args: varargs[string, `$`]): string =
  var parts: seq[string]
  for arg in args:
    try:
      let f = parseFloat(arg)
      parts.add($round(f))
    except ValueError:
      parts.add(arg)
  parts.join(" ")

template getFileInfo(): string =
  let info = instantiationInfo()
  if defaultLogger.fileName:
    extractFilename(info.filename) & ":" & $info.line
  else:
    info.filename.replace("\\", "/") & ":" & $info.line

proc writeLog(logger: Logger, level: LogLevel, msg: string,
    fileInfo: string) {.gcsafe.} =
  let mode = modes[ord(level) - 1]
  let timeStr = now().format("HH:mm:ss")

  let colorStart = if logger.useColor: mode.color else: ""
  let colorEnd = if logger.useColor: "\27[0m" else: ""
  let yellowStart = if logger.useColor: "\27[33m" else: ""

  let logMsg = &"{colorStart}[{mode.name.toUpper():<6}{timeStr}]{colorEnd} {yellowStart}{fileInfo}{colorEnd}: {msg}"
  echo logMsg

  if logger.outFile != "":
    try:
      let file = open(logger.outFile, fmAppend)
      defer: file.close()
      let plainMsg = &"[{mode.name.toUpper():<6}{now().format(\"yyyy-MM-dd HH:mm:ss\")}] {fileInfo}: {msg}\n"
      file.write(plainMsg)
    except IOError:
      discard

proc logWithLevel(level: LogLevel, args: varargs[string, `$`]) =
  if level >= defaultLogger.level:
    let fileInfo = getFileInfo()
    writeLog(defaultLogger, level, toLogString(args), fileInfo)

proc logTrace*(args: varargs[string, `$`]) {.gcsafe.} =
  {.cast(gcsafe).}: logWithLevel(LogLevel.Trace, args)
proc logDebug*(args: varargs[string, `$`]) {.gcsafe.} =
  {.cast(gcsafe).}: logWithLevel(LogLevel.Debug, args)
proc logInfo*(args: varargs[string, `$`]) {.gcsafe.} =
  {.cast(gcsafe).}: logWithLevel(LogLevel.Info, args)
proc logWarn*(args: varargs[string, `$`]) {.gcsafe.} =
  {.cast(gcsafe).}: logWithLevel(LogLevel.Warn, args)
proc logError*(args: varargs[string, `$`]) {.gcsafe.} =
  {.cast(gcsafe).}: logWithLevel(LogLevel.Error, args)
proc logFatal*(args: varargs[string, `$`]) {.gcsafe.} =
  {.cast(gcsafe).}: logWithLevel(LogLevel.Fatal, args)

# 兼容旧接口
proc log*(level: string, msg: auto) =
  case level.toLower():
  of "trace": logTrace($msg)
  of "debug": logDebug($msg)
  of "info": logInfo($msg)
  of "warn": logWarn($msg)
  of "error": logError($msg)
  of "fatal": logFatal($msg)
  else: logInfo($msg)
